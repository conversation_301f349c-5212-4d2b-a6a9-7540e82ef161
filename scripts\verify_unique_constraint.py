#!/usr/bin/env python3
"""
验证rule_detail表唯一约束脚本

用途：
1. 验证唯一约束是否正确添加
2. 测试重复数据插入是否被阻止
3. 确保约束功能正常工作

作者：系统管理员
创建时间：2025-07-30
"""

import sys
import os
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import IntegrityError

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import settings
from models.database import RuleDetail, RuleDetailStatusEnum, RuleTemplate, RuleTemplateStatusEnum


def verify_unique_constraint():
    """验证唯一约束"""
    print("=== 验证rule_detail表唯一约束 ===")

    # 获取数据库连接
    database_url = settings.get_database_url()
    if not database_url:
        print("❌ 无法获取数据库连接URL")
        return False

    engine = create_engine(database_url)
    Session = sessionmaker(bind=engine)

    try:
        with Session() as session:
            print(f"✅ 数据库连接成功: {database_url.split('@')[1] if '@' in database_url else 'local'}")

            # 1. 检查约束是否存在
            constraint_check = session.execute(
                text("""
                SELECT CONSTRAINT_NAME, CONSTRAINT_TYPE, TABLE_NAME
                FROM information_schema.TABLE_CONSTRAINTS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'rule_detail' 
                AND CONSTRAINT_NAME = 'uk_rule_detail_rule_id_key'
            """)
            ).fetchall()

            if not constraint_check:
                print("❌ 唯一约束 'uk_rule_detail_rule_id_key' 不存在")
                return False

            print("✅ 唯一约束 'uk_rule_detail_rule_id_key' 存在")
            for constraint_name, constraint_type, table_name in constraint_check:
                print(f"   - 约束名: {constraint_name}, 类型: {constraint_type}, 表: {table_name}")

            # 2. 检查约束字段
            columns_check = session.execute(
                text("""
                SELECT COLUMN_NAME, ORDINAL_POSITION
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'rule_detail' 
                AND CONSTRAINT_NAME = 'uk_rule_detail_rule_id_key'
                ORDER BY ORDINAL_POSITION
            """)
            ).fetchall()

            if columns_check:
                print("✅ 约束字段:")
                for column_name, position in columns_check:
                    print(f"   - 位置 {position}: {column_name}")
            else:
                print("❌ 无法获取约束字段信息")
                return False

            # 3. 测试约束功能
            print("\n=== 测试约束功能 ===")

            # 清理测试数据
            session.execute(text("DELETE FROM rule_detail WHERE rule_id LIKE 'test_%'"))
            session.execute(text("DELETE FROM rule_template WHERE rule_key LIKE 'test_%'"))
            session.commit()

            # 创建测试用的rule_template记录
            test_template1 = RuleTemplate(
                rule_key="test_rule_key",
                rule_type="测试类型",
                name="测试规则模板1",
                description="测试用规则模板",
                status=RuleTemplateStatusEnum.READY,
            )

            test_template2 = RuleTemplate(
                rule_key="different_rule_key",
                rule_type="测试类型",
                name="测试规则模板2",
                description="测试用规则模板2",
                status=RuleTemplateStatusEnum.READY,
            )

            session.add(test_template1)
            session.add(test_template2)
            session.commit()
            print("✅ 测试用rule_template记录创建成功")

            # 测试1: 插入第一条记录（应该成功）
            test_record1 = RuleDetail(
                rule_id="test_rule_001",
                rule_key="test_rule_key",
                rule_name="测试规则1",
                level1="测试类型1",
                level2="测试类型2",
                level3="测试类型3",
                error_reason="测试错误原因",
                degree="强制",
                reference="测试参考",
                detail_position="测试位置",
                prompted_fields1="test_field",
                type="测试",
                pos="通用",
                applicableArea="全国",
                default_use="是",
                remarks="测试备注",
                start_date="2024-01-01 00:00:00",
                end_date="2026-12-31 23:59:59",
                status=RuleDetailStatusEnum.ACTIVE,
            )

            try:
                session.add(test_record1)
                session.commit()
                print("✅ 第一条测试记录插入成功")
            except Exception as e:
                print(f"❌ 第一条测试记录插入失败: {e}")
                session.rollback()
                return False

            # 测试2: 插入重复记录（应该失败）
            test_record2 = RuleDetail(
                rule_id="test_rule_001",  # 相同的rule_id
                rule_key="test_rule_key",  # 相同的rule_key
                rule_name="测试规则2",
                level1="测试类型1",
                level2="测试类型2",
                level3="测试类型3",
                error_reason="测试错误原因2",
                degree="强制",
                reference="测试参考2",
                detail_position="测试位置2",
                prompted_fields1="test_field2",
                type="测试",
                pos="通用",
                applicableArea="全国",
                default_use="是",
                remarks="测试备注2",
                start_date="2024-01-01 00:00:00",
                end_date="2026-12-31 23:59:59",
                status=RuleDetailStatusEnum.ACTIVE,
            )

            try:
                session.add(test_record2)
                session.commit()
                print("❌ 重复记录插入成功（约束未生效）")
                return False
            except IntegrityError as e:
                session.rollback()
                if "uk_rule_detail_rule_id_key" in str(e) or "Duplicate entry" in str(e):
                    print("✅ 重复记录插入被正确阻止")
                else:
                    print(f"❌ 重复记录插入失败，但错误原因不明确: {e}")
                    return False
            except Exception as e:
                session.rollback()
                print(f"❌ 重复记录插入测试出现意外错误: {e}")
                return False

            # 测试3: 插入不同rule_key的相同rule_id（应该成功）
            test_record3 = RuleDetail(
                rule_id="test_rule_001",  # 相同的rule_id
                rule_key="different_rule_key",  # 不同的rule_key
                rule_name="测试规则3",
                level1="测试类型1",
                level2="测试类型2",
                level3="测试类型3",
                error_reason="测试错误原因3",
                degree="强制",
                reference="测试参考3",
                detail_position="测试位置3",
                prompted_fields1="test_field3",
                type="测试",
                pos="通用",
                applicableArea="全国",
                default_use="是",
                remarks="测试备注3",
                start_date="2024-01-01 00:00:00",
                end_date="2026-12-31 23:59:59",
                status=RuleDetailStatusEnum.ACTIVE,
            )

            try:
                session.add(test_record3)
                session.commit()
                print("✅ 不同rule_key的相同rule_id插入成功")
            except Exception as e:
                print(f"❌ 不同rule_key的相同rule_id插入失败: {e}")
                session.rollback()
                return False

            # 清理测试数据
            session.execute(text("DELETE FROM rule_detail WHERE rule_id LIKE 'test_%'"))
            session.execute(text("DELETE FROM rule_template WHERE rule_key LIKE 'test_%'"))
            session.commit()
            print("✅ 测试数据清理完成")

            return True

    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")
        return False
    finally:
        engine.dispose()


def main():
    """主函数"""
    print("开始验证rule_detail表唯一约束...")

    success = verify_unique_constraint()

    if success:
        print("\n✅ 唯一约束验证成功")
        sys.exit(0)
    else:
        print("\n❌ 唯一约束验证失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
