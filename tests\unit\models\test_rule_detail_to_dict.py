"""
测试RuleDetail的to_dict方法
"""

import pytest
from datetime import datetime
from models.database import RuleDetail, RuleDetailStatusEnum


class TestRuleDetailToDict:
    """测试RuleDetail的to_dict方法"""

    def test_rule_detail_to_dict_basic(self):
        """测试基本的to_dict功能"""
        # 创建RuleDetail实例
        detail = RuleDetail(
            rule_id="test_rule_001",
            rule_key="test_rule_key",
            rule_name="测试规则",
            level1="政策类",
            level2="药品政策限定类",
            level3="中药饮片单复方均不予支付",
            error_reason="测试错误原因",
            degree="强制",
            reference="测试参考资料",
            detail_position="费用信息",
            prompted_fields1="ybdm",
            type="非编码",
            pos="通用",
            applicableArea="全国",
            default_use="是",
            remarks="排除自费",
            start_date="2024-01-01 00:00:00",
            end_date="2026-12-31 23:59:59",
            status=RuleDetailStatusEnum.ACTIVE
        )

        # 设置数组字段（通过属性访问器）
        detail.yb_code = ["T001700914", "T001700915"]
        detail.diag_whole_code = ["D001", "D002"]

        # 调用to_dict方法
        result = detail.to_dict()

        # 验证基本字段
        assert result["rule_id"] == "test_rule_001"
        assert result["rule_key"] == "test_rule_key"
        assert result["rule_name"] == "测试规则"
        assert result["level1"] == "政策类"
        assert result["level2"] == "药品政策限定类"
        assert result["level3"] == "中药饮片单复方均不予支付"
        assert result["error_reason"] == "测试错误原因"
        assert result["degree"] == "强制"
        assert result["status"] == "ACTIVE"

        # 验证数组字段（应该返回列表）
        assert result["yb_code"] == ["T001700914", "T001700915"]
        assert result["diag_whole_code"] == ["D001", "D002"]

        # 验证时间字段（应该为None，因为没有设置）
        assert result["created_at"] is None
        assert result["updated_at"] is None

    def test_rule_detail_to_dict_with_timestamps(self):
        """测试包含时间戳的to_dict功能"""
        now = datetime.now()
        
        detail = RuleDetail(
            rule_id="test_rule_002",
            rule_key="test_rule_key",
            rule_name="时间戳测试规则",
            level1="测试类",
            status=RuleDetailStatusEnum.ACTIVE
        )
        
        # 手动设置时间戳
        detail.created_at = now
        detail.updated_at = now

        result = detail.to_dict()

        # 验证时间戳转换
        assert result["created_at"] == now.isoformat()
        assert result["updated_at"] == now.isoformat()

    def test_rule_detail_to_dict_with_extended_fields(self):
        """测试包含扩展字段的to_dict功能"""
        detail = RuleDetail(
            rule_id="test_rule_003",
            rule_key="test_rule_key",
            rule_name="扩展字段测试规则",
            level1="测试类",
            extended_fields='{"custom_field": "custom_value", "another_field": 123}',
            status=RuleDetailStatusEnum.ACTIVE
        )

        result = detail.to_dict()

        # 验证扩展字段
        assert result["extended_fields"] == '{"custom_field": "custom_value", "another_field": 123}'

    def test_rule_detail_to_dict_empty_array_fields(self):
        """测试空数组字段的处理"""
        detail = RuleDetail(
            rule_id="test_rule_004",
            rule_key="test_rule_key",
            rule_name="空数组字段测试",
            level1="测试类",
            status=RuleDetailStatusEnum.ACTIVE
        )

        # 数组字段默认应该为空列表
        result = detail.to_dict()

        # 验证空数组字段
        assert result["yb_code"] == []
        assert result["diag_whole_code"] == []
        assert result["diag_code_prefix"] == []
        assert result["fee_whole_code"] == []
        assert result["fee_code_prefix"] == []

    def test_rule_detail_to_dict_string_array_fields(self):
        """测试字符串格式的数组字段转换"""
        detail = RuleDetail(
            rule_id="test_rule_005",
            rule_key="test_rule_key",
            rule_name="字符串数组字段测试",
            level1="测试类",
            status=RuleDetailStatusEnum.ACTIVE
        )

        # 直接设置内部字符串字段
        detail._yb_code = "A001,A002,A003"
        detail._diag_whole_code = "D001,D002"
        detail._fee_whole_code = ""  # 空字符串

        result = detail.to_dict()

        # 验证数组字段转换（属性访问器应该将字符串转换为列表）
        assert result["yb_code"] == ["A001", "A002", "A003"]
        assert result["diag_whole_code"] == ["D001", "D002"]
        assert result["fee_whole_code"] == []  # 空字符串应该转换为空列表

    def test_rule_detail_to_dict_all_fields_present(self):
        """测试所有字段都存在的情况"""
        detail = RuleDetail(
            rule_id="test_rule_006",
            rule_key="test_rule_key",
            rule_name="完整字段测试规则",
            level1="政策类",
            level2="药品政策限定类",
            level3="中药饮片单复方均不予支付",
            error_reason="完整测试错误原因",
            degree="强制",
            reference="完整测试参考资料",
            detail_position="费用信息",
            prompted_fields3="test_field3",
            prompted_fields1="ybdm",
            type="非编码",
            pos="通用",
            applicableArea="全国",
            default_use="是",
            remarks="完整测试备注",
            in_illustration="完整测试说明",
            start_date="2024-01-01 00:00:00",
            end_date="2026-12-31 23:59:59",
            extended_fields='{"test": "value"}',
            status=RuleDetailStatusEnum.ACTIVE
        )

        # 设置数组字段
        detail.yb_code = ["T001700914"]
        detail.diag_whole_code = ["D001", "D002"]
        detail.diag_code_prefix = ["E10", "E11"]
        detail.diag_name_keyword = "糖尿病,高血压"
        detail.fee_whole_code = ["F001", "F002"]
        detail.fee_code_prefix = ["G001"]

        result = detail.to_dict()

        # 验证所有字段都存在
        expected_fields = [
            "id", "rule_id", "rule_key", "rule_name", "level1", "level2", "level3",
            "error_reason", "degree", "reference", "detail_position", "prompted_fields3",
            "prompted_fields1", "type", "pos", "applicableArea", "default_use",
            "remarks", "in_illustration", "start_date", "end_date", "yb_code",
            "diag_whole_code", "diag_code_prefix", "diag_name_keyword", "fee_whole_code",
            "fee_code_prefix", "extended_fields", "status", "created_at", "updated_at"
        ]

        for field in expected_fields:
            assert field in result, f"字段 {field} 不在结果中"

        # 验证数组字段类型
        assert isinstance(result["yb_code"], list)
        assert isinstance(result["diag_whole_code"], list)
        assert isinstance(result["diag_code_prefix"], list)
        assert isinstance(result["fee_whole_code"], list)
        assert isinstance(result["fee_code_prefix"], list)

    def test_rule_detail_to_dict_none_status(self):
        """测试状态为None的情况"""
        detail = RuleDetail(
            rule_id="test_rule_007",
            rule_key="test_rule_key",
            rule_name="状态为None测试",
            level1="测试类",
            status=None
        )

        result = detail.to_dict()

        # 验证状态为None时的处理
        assert result["status"] is None
