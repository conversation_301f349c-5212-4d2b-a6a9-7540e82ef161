#!/usr/bin/env python3
"""
综合测试脚本

用途：
1. 全面测试规则明细去重功能
2. 验证数据库约束、服务层逻辑、API接口的完整性
3. 模拟真实使用场景进行端到端测试

作者：系统管理员
创建时间：2025-07-30
"""

import sys
import os
import json
import requests
import time
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import IntegrityError

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import settings
from models.database import RuleTemplate, RuleTemplateStatusEnum, RuleDetail
from services.rule_detail_service import RuleDetailService


def test_database_constraints():
    """测试数据库约束"""
    print("=== 测试数据库约束 ===")

    database_url = settings.get_database_url()
    if not database_url:
        print("❌ 无法获取数据库连接URL")
        return False

    engine = create_engine(database_url)
    Session = sessionmaker(bind=engine)

    try:
        with Session() as session:
            # 清理测试数据
            session.execute(text("DELETE FROM rule_detail WHERE rule_id LIKE 'constraint_test_%'"))
            session.execute(text("DELETE FROM rule_template WHERE rule_key LIKE 'constraint_test_%'"))
            session.commit()

            # 创建测试模板
            test_template = RuleTemplate(
                rule_key="constraint_test_key",
                rule_type="约束测试类型",
                name="约束测试规则模板",
                description="用于测试数据库约束的规则模板",
                status=RuleTemplateStatusEnum.READY,
            )
            session.add(test_template)
            session.commit()

            # 测试1: 插入第一条记录（应该成功）
            session.execute(
                text("""
                INSERT INTO rule_detail (
                    rule_id, rule_key, rule_name, level1, level2, level3,
                    error_reason, degree, reference, detail_position,
                    prompted_fields1, type, pos, applicableArea, default_use,
                    remarks, start_date, end_date, status
                ) VALUES (
                    'constraint_test_001', 'constraint_test_key', '约束测试规则1',
                    '测试类型1', '测试类型2', '测试类型3', '测试错误原因',
                    '强制', '测试参考', '测试位置', 'test_field', '测试',
                    '通用', '全国', '是', '测试备注',
                    '2024-01-01 00:00:00', '2026-12-31 23:59:59', 'ACTIVE'
                )
            """)
            )
            session.commit()
            print("✅ 第一条记录插入成功")

            # 测试2: 插入重复记录（应该失败）
            try:
                session.execute(
                    text("""
                    INSERT INTO rule_detail (
                        rule_id, rule_key, rule_name, level1, level2, level3,
                        error_reason, degree, reference, detail_position,
                        prompted_fields1, type, pos, applicableArea, default_use,
                        remarks, start_date, end_date, status
                    ) VALUES (
                        'constraint_test_001', 'constraint_test_key', '约束测试规则1重复',
                        '测试类型1', '测试类型2', '测试类型3', '测试错误原因',
                        '强制', '测试参考', '测试位置', 'test_field', '测试',
                        '通用', '全国', '是', '测试备注',
                        '2024-01-01 00:00:00', '2026-12-31 23:59:59', 'ACTIVE'
                    )
                """)
                )
                session.commit()
                print("❌ 重复记录插入成功（约束未生效）")
                return False
            except IntegrityError:
                session.rollback()
                print("✅ 重复记录插入被正确阻止")

            # 测试3: 不同rule_key的相同rule_id（应该成功）
            session.execute(
                text("""
                INSERT INTO rule_template (rule_key, rule_type, name, description, status)
                VALUES ('constraint_test_key2', '约束测试类型2', '约束测试规则模板2', '测试模板2', 'READY')
            """)
            )

            session.execute(
                text("""
                INSERT INTO rule_detail (
                    rule_id, rule_key, rule_name, level1, level2, level3,
                    error_reason, degree, reference, detail_position,
                    prompted_fields1, type, pos, applicableArea, default_use,
                    remarks, start_date, end_date, status
                ) VALUES (
                    'constraint_test_001', 'constraint_test_key2', '约束测试规则1（不同key）',
                    '测试类型1', '测试类型2', '测试类型3', '测试错误原因',
                    '强制', '测试参考', '测试位置', 'test_field', '测试',
                    '通用', '全国', '是', '测试备注',
                    '2024-01-01 00:00:00', '2026-12-31 23:59:59', 'ACTIVE'
                )
            """)
            )
            session.commit()
            print("✅ 不同rule_key的相同rule_id插入成功")

            # 清理测试数据
            session.execute(text("DELETE FROM rule_detail WHERE rule_id LIKE 'constraint_test_%'"))
            session.execute(text("DELETE FROM rule_template WHERE rule_key LIKE 'constraint_test_%'"))
            session.commit()

            return True

    except Exception as e:
        print(f"❌ 数据库约束测试失败: {e}")
        return False
    finally:
        engine.dispose()


def test_service_layer():
    """测试服务层"""
    print("\n=== 测试服务层 ===")

    database_url = settings.get_database_url()
    if not database_url:
        print("❌ 无法获取数据库连接URL")
        return False

    engine = create_engine(database_url)
    Session = sessionmaker(bind=engine)

    try:
        with Session() as session:
            # 清理测试数据
            session.query(RuleDetail).filter(RuleDetail.rule_id.like("service_test_%")).delete()
            session.query(RuleTemplate).filter(RuleTemplate.rule_key.like("service_test_%")).delete()
            session.commit()

            # 创建测试模板
            test_template = RuleTemplate(
                rule_key="service_test_key",
                rule_type="服务测试类型",
                name="服务测试规则模板",
                description="用于测试服务层的规则模板",
                status=RuleTemplateStatusEnum.READY,
            )
            session.add(test_template)
            session.commit()

            # 初始化服务
            rule_service = RuleDetailService(session)

            # 测试数据 - 注意必须包含rule_key字段
            test_data = [
                {
                    "rule_id": "service_test_001",
                    "rule_key": "service_test_key",  # 添加rule_key字段
                    "rule_name": "服务测试规则1",
                    "level1": "服务测试类型1",
                    "level2": "服务测试类型2",
                    "level3": "服务测试类型3",
                    "error_reason": "服务测试错误原因1",
                    "degree": "强制",
                    "reference": "服务测试参考1",
                    "detail_position": "服务测试位置1",
                    "prompted_fields1": "service_test_field1",
                    "type": "服务测试",
                    "pos": "通用",
                    "applicableArea": "全国",
                    "default_use": "是",
                    "remarks": "服务测试备注1",
                    "start_date": "2024-01-01 00:00:00",
                    "end_date": "2026-12-31 23:59:59",
                },
                {
                    "rule_id": "service_test_002",
                    "rule_key": "service_test_key",  # 添加rule_key字段
                    "rule_name": "服务测试规则2",
                    "level1": "服务测试类型1",
                    "level2": "服务测试类型2",
                    "level3": "服务测试类型3",
                    "error_reason": "服务测试错误原因2",
                    "degree": "强制",
                    "reference": "服务测试参考2",
                    "detail_position": "服务测试位置2",
                    "prompted_fields1": "service_test_field2",
                    "type": "服务测试",
                    "pos": "通用",
                    "applicableArea": "全国",
                    "default_use": "是",
                    "remarks": "服务测试备注2",
                    "start_date": "2024-01-01 00:00:00",
                    "end_date": "2026-12-31 23:59:59",
                },
            ]

            # 测试批量upsert
            result = rule_service.batch_upsert_rule_details("service_test_key", test_data)

            print(f"批量upsert结果:")
            print(f"  总数: {result['total_count']}")
            print(f"  新建: {result['created_count']}")
            print(f"  更新: {result['updated_count']}")
            print(f"  跳过: {result['skipped_count']}")
            print(f"  失败: {result['failed_count']}")

            # 验证结果
            if result["created_count"] != 2 or result["failed_count"] != 0:
                print("❌ 批量upsert结果不符合预期")
                return False

            print("✅ 服务层测试通过")

            # 清理测试数据
            session.query(RuleDetail).filter(RuleDetail.rule_id.like("service_test_%")).delete()
            session.query(RuleTemplate).filter(RuleTemplate.rule_key.like("service_test_%")).delete()
            session.commit()

            return True

    except Exception as e:
        print(f"❌ 服务层测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False
    finally:
        engine.dispose()


def test_api_layer():
    """测试API层"""
    print("\n=== 测试API层 ===")

    # API配置
    base_url = "http://localhost:18001"
    api_key = "a_very_secret_key_for_development"
    headers = {"X-API-KEY": api_key, "Content-Type": "application/json"}

    # 检查服务是否运行
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code != 200:
            print("❌ 后端服务未运行，请先启动服务")
            return False
    except requests.exceptions.RequestException:
        print("❌ 无法连接到后端服务，请确保服务运行在 http://localhost:18001")
        return False

    # 准备测试数据
    database_url = settings.get_database_url()
    engine = create_engine(database_url)
    Session = sessionmaker(bind=engine)

    try:
        with Session() as session:
            # 清理测试数据
            session.query(RuleDetail).filter(RuleDetail.rule_id.like("api_test_%")).delete()
            session.query(RuleTemplate).filter(RuleTemplate.rule_key.like("api_test_%")).delete()
            session.commit()

            # 创建测试模板
            test_template = RuleTemplate(
                rule_key="api_test_key",
                rule_type="API测试类型",
                name="API测试规则模板",
                description="用于测试API层的规则模板",
                status=RuleTemplateStatusEnum.READY,
            )
            session.add(test_template)
            session.commit()

            # 测试数据
            test_data = [
                {
                    "rule_id": "api_test_001",
                    "rule_name": "API测试规则1",
                    "level1": "API测试类型1",
                    "level2": "API测试类型2",
                    "level3": "API测试类型3",
                    "error_reason": "API测试错误原因1",
                    "degree": "强制",
                    "reference": "API测试参考1",
                    "detail_position": "API测试位置1",
                    "prompted_fields1": "api_test_field1",
                    "type": "API测试",
                    "pos": "通用",
                    "applicableArea": "全国",
                    "default_use": "是",
                    "remarks": "API测试备注1",
                    "start_date": "2024-01-01 00:00:00",
                    "end_date": "2026-12-31 23:59:59",
                }
            ]

            payload = {"user_id": "comprehensive_test_user", "data_to_submit": test_data}

            # 首次提交
            response = requests.post(
                f"{base_url}/api/v1/rules/api_test_key/confirm_submission", headers=headers, json=payload, timeout=30
            )

            if response.status_code != 201:
                print(f"❌ API调用失败: {response.status_code}")
                return False

            result = response.json()
            operation_details = result.get("operation_details", {})

            if operation_details.get("created_count") != 1:
                print(f"❌ API响应不符合预期: {operation_details}")
                return False

            print("✅ API层测试通过")

            # 清理测试数据
            session.query(RuleDetail).filter(RuleDetail.rule_id.like("api_test_%")).delete()
            session.query(RuleTemplate).filter(RuleTemplate.rule_key.like("api_test_%")).delete()
            session.commit()

            return True

    except Exception as e:
        print(f"❌ API层测试失败: {e}")
        return False
    finally:
        engine.dispose()


def main():
    """主函数"""
    print("开始综合测试...")

    # 执行各层测试
    tests = [("数据库约束", test_database_constraints), ("服务层", test_service_layer), ("API层", test_api_layer)]

    passed = 0
    failed = 0

    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"执行 {test_name} 测试...")

        if test_func():
            print(f"✅ {test_name} 测试通过")
            passed += 1
        else:
            print(f"❌ {test_name} 测试失败")
            failed += 1

    # 输出总结
    print(f"\n{'='*50}")
    print("综合测试完成")
    print(f"通过: {passed}/{len(tests)}")
    print(f"失败: {failed}/{len(tests)}")

    if failed == 0:
        print("\n🎉 所有测试通过！规则明细去重功能已成功实现")
        sys.exit(0)
    else:
        print(f"\n❌ 有 {failed} 个测试失败，请检查相关功能")
        sys.exit(1)


if __name__ == "__main__":
    main()
