#!/usr/bin/env python3
"""
测试API层Upsert功能脚本

用途：
1. 测试confirm_submission API使用upsert逻辑
2. 验证API响应格式的正确性
3. 确保统计信息准确

作者：系统管理员
创建时间：2025-07-30
"""

import sys
import os
import json
import requests
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import settings
from models.database import RuleTemplate, RuleTemplateStatusEnum, RuleDetail


def test_api_upsert():
    """测试API层Upsert功能"""
    print("=== 测试API层Upsert功能 ===")
    
    # API配置
    base_url = "http://localhost:18001"
    api_key = "a_very_secret_key_for_development"
    headers = {
        "X-API-KEY": api_key,
        "Content-Type": "application/json"
    }
    
    # 获取数据库连接用于数据准备和清理
    database_url = settings.get_database_url()
    if not database_url:
        print("❌ 无法获取数据库连接URL")
        return False
    
    engine = create_engine(database_url)
    Session = sessionmaker(bind=engine)
    
    try:
        with Session() as session:
            print(f"✅ 数据库连接成功: {database_url.split('@')[1] if '@' in database_url else 'local'}")
            
            # 清理测试数据
            session.query(RuleDetail).filter(RuleDetail.rule_id.like('api_test_%')).delete()
            session.query(RuleTemplate).filter(RuleTemplate.rule_key.like('api_test_%')).delete()
            session.commit()
            
            # 创建测试用的rule_template
            test_template = RuleTemplate(
                rule_key="api_test_key",
                rule_type="API测试类型",
                name="API测试规则模板",
                description="用于测试API Upsert功能的规则模板",
                status=RuleTemplateStatusEnum.READY,
            )
            session.add(test_template)
            session.commit()
            print("✅ 测试用rule_template创建成功")
            
            # 测试数据
            test_data = [
                {
                    "rule_id": "api_test_001",
                    "rule_name": "API测试规则1",
                    "level1": "API测试类型1",
                    "level2": "API测试类型2",
                    "level3": "API测试类型3",
                    "error_reason": "API测试错误原因1",
                    "degree": "强制",
                    "reference": "API测试参考1",
                    "detail_position": "API测试位置1",
                    "prompted_fields1": "api_test_field1",
                    "type": "API测试",
                    "pos": "通用",
                    "applicableArea": "全国",
                    "default_use": "是",
                    "remarks": "API测试备注1",
                    "start_date": "2024-01-01 00:00:00",
                    "end_date": "2026-12-31 23:59:59"
                },
                {
                    "rule_id": "api_test_002",
                    "rule_name": "API测试规则2",
                    "level1": "API测试类型1",
                    "level2": "API测试类型2",
                    "level3": "API测试类型3",
                    "error_reason": "API测试错误原因2",
                    "degree": "强制",
                    "reference": "API测试参考2",
                    "detail_position": "API测试位置2",
                    "prompted_fields1": "api_test_field2",
                    "type": "API测试",
                    "pos": "通用",
                    "applicableArea": "全国",
                    "default_use": "是",
                    "remarks": "API测试备注2",
                    "start_date": "2024-01-01 00:00:00",
                    "end_date": "2026-12-31 23:59:59"
                }
            ]
            
            # 测试1: 首次提交（应该全部创建）
            print("\n--- 测试1: 首次提交 ---")
            payload = {
                "user_id": "api_test_user",
                "data_to_submit": test_data
            }
            
            response = requests.post(
                f"{base_url}/api/v1/rules/api_test_key/confirm_submission",
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 201:
                result = response.json()
                print(f"✅ API调用成功")
                print(f"响应消息: {result.get('message', 'N/A')}")
                print(f"总数: {result.get('total_items', 0)}")
                print(f"成功数: {result.get('success_count', 0)}")
                print(f"失败数: {result.get('failed_count', 0)}")
                
                # 检查详细统计
                operation_details = result.get('operation_details', {})
                print(f"操作详情:")
                print(f"  新建: {operation_details.get('created_count', 0)}")
                print(f"  更新: {operation_details.get('updated_count', 0)}")
                print(f"  跳过: {operation_details.get('skipped_count', 0)}")
                print(f"  失败: {operation_details.get('failed_count', 0)}")
                
                # 验证结果
                assert result.get('total_items') == 2, "总数应该是2"
                assert operation_details.get('created_count') == 2, "应该新建2条记录"
                assert operation_details.get('updated_count') == 0, "应该更新0条记录"
                assert operation_details.get('skipped_count') == 0, "应该跳过0条记录"
                assert operation_details.get('failed_count') == 0, "应该失败0条记录"
                print("✅ 首次提交测试通过")
            else:
                print(f"❌ API调用失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                return False
            
            # 测试2: 重复提交（应该全部跳过）
            print("\n--- 测试2: 重复提交 ---")
            response = requests.post(
                f"{base_url}/api/v1/rules/api_test_key/confirm_submission",
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 201:
                result = response.json()
                print(f"✅ API调用成功")
                
                operation_details = result.get('operation_details', {})
                print(f"操作详情:")
                print(f"  新建: {operation_details.get('created_count', 0)}")
                print(f"  更新: {operation_details.get('updated_count', 0)}")
                print(f"  跳过: {operation_details.get('skipped_count', 0)}")
                print(f"  失败: {operation_details.get('failed_count', 0)}")
                
                # 验证结果
                assert operation_details.get('created_count') == 0, "应该新建0条记录"
                assert operation_details.get('updated_count') == 0, "应该更新0条记录"
                assert operation_details.get('skipped_count') == 2, "应该跳过2条记录"
                assert operation_details.get('failed_count') == 0, "应该失败0条记录"
                print("✅ 重复提交测试通过")
            else:
                print(f"❌ API调用失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                return False
            
            # 测试3: 修改数据后提交（应该更新）
            print("\n--- 测试3: 修改数据后提交 ---")
            modified_data = test_data.copy()
            modified_data[0]["rule_name"] = "API测试规则1（已修改）"
            modified_data[0]["error_reason"] = "API测试错误原因1（已修改）"
            
            payload_modified = {
                "user_id": "api_test_user",
                "data_to_submit": modified_data
            }
            
            response = requests.post(
                f"{base_url}/api/v1/rules/api_test_key/confirm_submission",
                headers=headers,
                json=payload_modified,
                timeout=30
            )
            
            if response.status_code == 201:
                result = response.json()
                print(f"✅ API调用成功")
                
                operation_details = result.get('operation_details', {})
                print(f"操作详情:")
                print(f"  新建: {operation_details.get('created_count', 0)}")
                print(f"  更新: {operation_details.get('updated_count', 0)}")
                print(f"  跳过: {operation_details.get('skipped_count', 0)}")
                print(f"  失败: {operation_details.get('failed_count', 0)}")
                
                # 验证结果
                assert operation_details.get('created_count') == 0, "应该新建0条记录"
                assert operation_details.get('updated_count') == 1, "应该更新1条记录"
                assert operation_details.get('skipped_count') == 1, "应该跳过1条记录"
                assert operation_details.get('failed_count') == 0, "应该失败0条记录"
                print("✅ 修改数据提交测试通过")
            else:
                print(f"❌ API调用失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                return False
            
            # 清理测试数据
            session.query(RuleDetail).filter(RuleDetail.rule_id.like('api_test_%')).delete()
            session.query(RuleTemplate).filter(RuleTemplate.rule_key.like('api_test_%')).delete()
            session.commit()
            print("✅ 测试数据清理完成")
            
            return True
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        engine.dispose()


def main():
    """主函数"""
    print("开始测试API层Upsert功能...")
    print("注意：此测试需要后端服务运行在 http://localhost:18001")
    
    success = test_api_upsert()
    
    if success:
        print("\n✅ API层Upsert功能测试成功")
        sys.exit(0)
    else:
        print("\n❌ API层Upsert功能测试失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
