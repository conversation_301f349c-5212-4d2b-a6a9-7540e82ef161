#!/usr/bin/env python3
"""
测试前端集成脚本

用途：
1. 启动后端服务
2. 模拟前端请求测试完整流程
3. 验证API响应格式是否符合前端预期

作者：系统管理员
创建时间：2025-07-30
"""

import sys
import os
import json
import requests
import time
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import settings
from models.database import RuleTemplate, RuleTemplateStatusEnum, RuleDetail


def test_frontend_integration():
    """测试前端集成"""
    print("=== 测试前端集成 ===")
    
    # API配置
    base_url = "http://localhost:18001"
    api_key = "a_very_secret_key_for_development"
    headers = {
        "X-API-KEY": api_key,
        "Content-Type": "application/json"
    }
    
    # 获取数据库连接用于数据准备和清理
    database_url = settings.get_database_url()
    if not database_url:
        print("❌ 无法获取数据库连接URL")
        return False
    
    engine = create_engine(database_url)
    Session = sessionmaker(bind=engine)
    
    try:
        with Session() as session:
            print(f"✅ 数据库连接成功: {database_url.split('@')[1] if '@' in database_url else 'local'}")
            
            # 清理测试数据
            session.query(RuleDetail).filter(RuleDetail.rule_id.like('frontend_test_%')).delete()
            session.query(RuleTemplate).filter(RuleTemplate.rule_key.like('frontend_test_%')).delete()
            session.commit()
            
            # 创建测试用的rule_template
            test_template = RuleTemplate(
                rule_key="frontend_test_key",
                rule_type="前端测试类型",
                name="前端集成测试规则模板",
                description="用于测试前端集成的规则模板",
                status=RuleTemplateStatusEnum.READY,
            )
            session.add(test_template)
            session.commit()
            print("✅ 测试用rule_template创建成功")
            
            # 模拟前端上传流程
            print("\n--- 模拟前端上传流程 ---")
            
            # 1. 获取规则信息（模拟前端初始化）
            print("1. 获取规则信息...")
            response = requests.get(
                f"{base_url}/api/v1/rules/frontend_test_key",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                rule_info = response.json()
                print(f"✅ 规则信息获取成功: {rule_info.get('name', 'N/A')}")
            else:
                print(f"❌ 获取规则信息失败: {response.status_code}")
                return False
            
            # 2. 模拟数据提交（首次上传）
            print("\n2. 首次数据提交...")
            test_data = [
                {
                    "rule_id": "frontend_test_001",
                    "rule_name": "前端测试规则1",
                    "level1": "前端测试类型1",
                    "level2": "前端测试类型2",
                    "level3": "前端测试类型3",
                    "error_reason": "前端测试错误原因1",
                    "degree": "强制",
                    "reference": "前端测试参考1",
                    "detail_position": "前端测试位置1",
                    "prompted_fields1": "frontend_test_field1",
                    "type": "前端测试",
                    "pos": "通用",
                    "applicableArea": "全国",
                    "default_use": "是",
                    "remarks": "前端测试备注1",
                    "start_date": "2024-01-01 00:00:00",
                    "end_date": "2026-12-31 23:59:59"
                },
                {
                    "rule_id": "frontend_test_002",
                    "rule_name": "前端测试规则2",
                    "level1": "前端测试类型1",
                    "level2": "前端测试类型2",
                    "level3": "前端测试类型3",
                    "error_reason": "前端测试错误原因2",
                    "degree": "强制",
                    "reference": "前端测试参考2",
                    "detail_position": "前端测试位置2",
                    "prompted_fields1": "frontend_test_field2",
                    "type": "前端测试",
                    "pos": "通用",
                    "applicableArea": "全国",
                    "default_use": "是",
                    "remarks": "前端测试备注2",
                    "start_date": "2024-01-01 00:00:00",
                    "end_date": "2026-12-31 23:59:59"
                }
            ]
            
            payload = {
                "user_id": "frontend_test_user",
                "data_to_submit": test_data
            }
            
            response = requests.post(
                f"{base_url}/api/v1/rules/frontend_test_key/confirm_submission",
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 201:
                result = response.json()
                print(f"✅ 首次提交成功")
                
                # 验证响应格式是否符合前端预期
                print("\n--- 验证响应格式 ---")
                required_fields = [
                    'message', 'total_items', 'success_count', 'failed_count',
                    'storage_mode', 'operation_details', 'processing_summary'
                ]
                
                missing_fields = []
                for field in required_fields:
                    if field not in result:
                        missing_fields.append(field)
                
                if missing_fields:
                    print(f"❌ 响应缺少必需字段: {missing_fields}")
                    return False
                
                print("✅ 响应格式验证通过")
                
                # 验证operation_details结构
                operation_details = result.get('operation_details', {})
                detail_fields = ['created_count', 'updated_count', 'skipped_count', 'failed_count']
                
                for field in detail_fields:
                    if field not in operation_details:
                        print(f"❌ operation_details缺少字段: {field}")
                        return False
                
                print("✅ operation_details结构验证通过")
                
                # 验证processing_summary结构
                processing_summary = result.get('processing_summary', {})
                summary_fields = ['new_records', 'updated_records', 'duplicate_records_skipped', 'failed_records']
                
                for field in summary_fields:
                    if field not in processing_summary:
                        print(f"❌ processing_summary缺少字段: {field}")
                        return False
                
                print("✅ processing_summary结构验证通过")
                
                # 打印完整响应供前端开发参考
                print("\n--- 完整API响应示例 ---")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                
            else:
                print(f"❌ 首次提交失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                return False
            
            # 3. 模拟重复提交（应该跳过）
            print("\n3. 重复数据提交...")
            response = requests.post(
                f"{base_url}/api/v1/rules/frontend_test_key/confirm_submission",
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 201:
                result = response.json()
                operation_details = result.get('operation_details', {})
                
                if operation_details.get('skipped_count') == 2:
                    print("✅ 重复数据正确跳过")
                else:
                    print(f"❌ 重复数据处理异常: {operation_details}")
                    return False
            else:
                print(f"❌ 重复提交测试失败: {response.status_code}")
                return False
            
            # 清理测试数据
            session.query(RuleDetail).filter(RuleDetail.rule_id.like('frontend_test_%')).delete()
            session.query(RuleTemplate).filter(RuleTemplate.rule_key.like('frontend_test_%')).delete()
            session.commit()
            print("✅ 测试数据清理完成")
            
            return True
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        engine.dispose()


def main():
    """主函数"""
    print("开始测试前端集成...")
    print("注意：此测试需要后端服务运行在 http://localhost:18001")
    
    success = test_frontend_integration()
    
    if success:
        print("\n✅ 前端集成测试成功")
        print("\n📋 前端开发要点:")
        print("1. API响应包含详细的operation_details统计信息")
        print("2. processing_summary提供用户友好的摘要")
        print("3. 可以根据各项统计数据显示不同的用户提示")
        print("4. 建议在UI中突出显示跳过的重复记录数量")
        sys.exit(0)
    else:
        print("\n❌ 前端集成测试失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
