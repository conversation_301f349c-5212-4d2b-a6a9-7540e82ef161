#!/usr/bin/env python3
"""
测试Upsert功能脚本

用途：
1. 测试upsert_rule_detail方法的各种场景
2. 验证数据比对逻辑的正确性
3. 确保批量upsert功能正常工作

作者：系统管理员
创建时间：2025-07-30
"""

import sys
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import settings
from models.database import RuleTemplate, RuleTemplateStatusEnum, RuleDetail
from services.rule_detail_service import RuleDetailService


def test_upsert_functionality():
    """测试Upsert功能"""
    print("=== 测试Upsert功能 ===")
    
    # 获取数据库连接
    database_url = settings.get_database_url()
    if not database_url:
        print("❌ 无法获取数据库连接URL")
        return False
    
    engine = create_engine(database_url)
    Session = sessionmaker(bind=engine)
    
    try:
        with Session() as session:
            print(f"✅ 数据库连接成功: {database_url.split('@')[1] if '@' in database_url else 'local'}")
            
            # 清理测试数据
            session.query(RuleDetail).filter(RuleDetail.rule_id.like('upsert_test_%')).delete()
            session.query(RuleTemplate).filter(RuleTemplate.rule_key.like('upsert_test_%')).delete()
            session.commit()
            
            # 创建测试用的rule_template
            test_template = RuleTemplate(
                rule_key="upsert_test_key",
                rule_type="测试类型",
                name="Upsert测试规则模板",
                description="用于测试Upsert功能的规则模板",
                status=RuleTemplateStatusEnum.READY,
            )
            session.add(test_template)
            session.commit()
            print("✅ 测试用rule_template创建成功")
            
            # 初始化服务
            rule_service = RuleDetailService(session)
            
            # 测试数据
            test_data = {
                "rule_id": "upsert_test_001",
                "rule_key": "upsert_test_key",
                "rule_name": "测试规则1",
                "level1": "测试类型1",
                "level2": "测试类型2",
                "level3": "测试类型3",
                "error_reason": "测试错误原因",
                "degree": "强制",
                "reference": "测试参考",
                "detail_position": "测试位置",
                "prompted_fields1": "test_field",
                "type": "测试",
                "pos": "通用",
                "applicableArea": "全国",
                "default_use": "是",
                "remarks": "测试备注",
                "start_date": "2024-01-01 00:00:00",
                "end_date": "2026-12-31 23:59:59"
            }
            
            # 测试1: 首次插入（应该创建）
            print("\n--- 测试1: 首次插入 ---")
            result1, operation1 = rule_service.upsert_rule_detail("upsert_test_key", test_data)
            print(f"操作类型: {operation1}")
            print(f"记录ID: {result1.id}")
            assert operation1 == "CREATED", f"期望CREATED，实际{operation1}"
            print("✅ 首次插入测试通过")
            
            # 测试2: 相同数据再次插入（应该跳过）
            print("\n--- 测试2: 相同数据再次插入 ---")
            result2, operation2 = rule_service.upsert_rule_detail("upsert_test_key", test_data)
            print(f"操作类型: {operation2}")
            print(f"记录ID: {result2.id}")
            assert operation2 == "SKIPPED", f"期望SKIPPED，实际{operation2}"
            assert result2.id == result1.id, "应该返回相同的记录"
            print("✅ 跳过重复数据测试通过")
            
            # 测试3: 修改数据后插入（应该更新）
            print("\n--- 测试3: 修改数据后插入 ---")
            modified_data = test_data.copy()
            modified_data["rule_name"] = "测试规则1（已修改）"
            modified_data["error_reason"] = "测试错误原因（已修改）"
            
            result3, operation3 = rule_service.upsert_rule_detail("upsert_test_key", modified_data)
            print(f"操作类型: {operation3}")
            print(f"记录ID: {result3.id}")
            print(f"更新后的规则名称: {result3.rule_name}")
            assert operation3 == "UPDATED", f"期望UPDATED，实际{operation3}"
            assert result3.id == result1.id, "应该更新相同的记录"
            assert result3.rule_name == "测试规则1（已修改）", "规则名称应该被更新"
            print("✅ 数据更新测试通过")
            
            # 测试4: 批量Upsert
            print("\n--- 测试4: 批量Upsert ---")
            batch_data = [
                {
                    "rule_id": "upsert_test_002",
                    "rule_key": "upsert_test_key",
                    "rule_name": "批量测试规则2",
                    "level1": "批量测试类型1",
                    "level2": "批量测试类型2",
                    "level3": "批量测试类型3",
                    "error_reason": "批量测试错误原因",
                    "degree": "强制",
                    "reference": "批量测试参考",
                    "detail_position": "批量测试位置",
                    "prompted_fields1": "batch_test_field",
                    "type": "批量测试",
                    "pos": "通用",
                    "applicableArea": "全国",
                    "default_use": "是",
                    "remarks": "批量测试备注",
                    "start_date": "2024-01-01 00:00:00",
                    "end_date": "2026-12-31 23:59:59"
                },
                {
                    "rule_id": "upsert_test_003",
                    "rule_key": "upsert_test_key",
                    "rule_name": "批量测试规则3",
                    "level1": "批量测试类型1",
                    "level2": "批量测试类型2",
                    "level3": "批量测试类型3",
                    "error_reason": "批量测试错误原因",
                    "degree": "强制",
                    "reference": "批量测试参考",
                    "detail_position": "批量测试位置",
                    "prompted_fields1": "batch_test_field",
                    "type": "批量测试",
                    "pos": "通用",
                    "applicableArea": "全国",
                    "default_use": "是",
                    "remarks": "批量测试备注",
                    "start_date": "2024-01-01 00:00:00",
                    "end_date": "2026-12-31 23:59:59"
                },
                # 重复第一条记录（应该跳过）
                modified_data
            ]
            
            batch_result = rule_service.batch_upsert_rule_details("upsert_test_key", batch_data)
            print(f"批量操作结果:")
            print(f"  总数: {batch_result['total_count']}")
            print(f"  新建: {batch_result['created_count']}")
            print(f"  更新: {batch_result['updated_count']}")
            print(f"  跳过: {batch_result['skipped_count']}")
            print(f"  失败: {batch_result['failed_count']}")
            
            assert batch_result['total_count'] == 3, "总数应该是3"
            assert batch_result['created_count'] == 2, "应该新建2条记录"
            assert batch_result['updated_count'] == 0, "应该更新0条记录"
            assert batch_result['skipped_count'] == 1, "应该跳过1条记录"
            assert batch_result['failed_count'] == 0, "应该失败0条记录"
            print("✅ 批量Upsert测试通过")
            
            # 清理测试数据
            session.query(RuleDetail).filter(RuleDetail.rule_id.like('upsert_test_%')).delete()
            session.query(RuleTemplate).filter(RuleTemplate.rule_key.like('upsert_test_%')).delete()
            session.commit()
            print("✅ 测试数据清理完成")
            
            return True
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        engine.dispose()


def main():
    """主函数"""
    print("开始测试Upsert功能...")
    
    success = test_upsert_functionality()
    
    if success:
        print("\n✅ Upsert功能测试成功")
        sys.exit(0)
    else:
        print("\n❌ Upsert功能测试失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
