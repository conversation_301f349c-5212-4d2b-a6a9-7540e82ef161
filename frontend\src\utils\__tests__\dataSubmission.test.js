/**
 * 数据提交流程测试
 * 验证数组字段转换和数据提交的正确性
 */

import { describe, it, expect } from 'vitest'
import { convertExcelRowArrayFields } from '../arrayFieldConverter'

describe('数据提交流程测试', () => {
  describe('allValidRows 数据结构测试', () => {
    it('应该正确处理直接的数据对象', () => {
      // 模拟 allValidRows 中的数据结构
      const mockValidRows = [
        {
          rule_name: '成人用药限制规则',
          yb_code: 'A001,A002,A003',
          diag_whole_code: 'D001;D002',
          level1: '一级错误'
        },
        {
          rule_name: '儿童用药限制规则', 
          yb_code: 'B001,B002',
          diag_whole_code: 'D003;D004;D005',
          level1: '二级错误'
        }
      ]

      // 模拟 confirmSubmission 中的处理逻辑
      const processedData = mockValidRows.map(row => {
        console.log('转换前:', row)
        const convertedData = convertExcelRowArrayFields(row)
        console.log('转换后:', convertedData)
        return convertedData
      })

      // 验证结果
      expect(processedData).toHaveLength(2)
      
      // 验证第一条数据
      expect(processedData[0].rule_name).toBe('成人用药限制规则')
      expect(processedData[0].yb_code).toEqual(['A001', 'A002', 'A003'])
      expect(processedData[0].diag_whole_code).toEqual(['D001', 'D002'])
      expect(processedData[0].level1).toBe('一级错误')
      
      // 验证第二条数据
      expect(processedData[1].rule_name).toBe('儿童用药限制规则')
      expect(processedData[1].yb_code).toEqual(['B001', 'B002'])
      expect(processedData[1].diag_whole_code).toEqual(['D003', 'D004', 'D005'])
      expect(processedData[1].level1).toBe('二级错误')
    })

    it('应该正确处理空数据或无效数据', () => {
      const invalidInputs = [
        null,
        undefined,
        '',
        'not an object',
        123,
        []
      ]

      invalidInputs.forEach(input => {
        const result = convertExcelRowArrayFields(input)
        expect(result).toEqual({})
      })
    })

    it('应该正确处理包含空数组字段的数据', () => {
      const rowData = {
        rule_name: '测试规则',
        yb_code: '',  // 空的数组字段
        diag_whole_code: null,  // null 的数组字段
        level1: '一级错误'
      }

      const result = convertExcelRowArrayFields(rowData)

      expect(result.rule_name).toBe('测试规则')
      expect(result.yb_code).toEqual([])  // 空字符串应该转换为空数组
      expect(result.diag_whole_code).toEqual([])  // null 应该转换为空数组
      expect(result.level1).toBe('一级错误')
    })
  })

  describe('数据提交格式验证', () => {
    it('提交的数据应该是纯数据对象数组', () => {
      const mockValidRows = [
        {
          rule_name: '测试规则1',
          yb_code: 'A001,A002',
          level1: '一级错误'
        },
        {
          rule_name: '测试规则2',
          yb_code: 'B001,B002',
          level1: '二级错误'
        }
      ]

      // 模拟提交数据的处理
      const processedData = mockValidRows.map(row => convertExcelRowArrayFields(row))

      // 验证数据格式
      expect(Array.isArray(processedData)).toBe(true)
      expect(processedData).toHaveLength(2)
      
      // 验证每个元素都是纯数据对象
      processedData.forEach(item => {
        expect(typeof item).toBe('object')
        expect(item).not.toBeNull()
        expect('rule_name' in item).toBe(true)
        expect('yb_code' in item).toBe(true)
        expect(Array.isArray(item.yb_code)).toBe(true)
      })
    })

    it('应该生成符合后端期望的数据格式', () => {
      const mockRow = {
        rule_name: '成人用药限制规则',
        yb_code: 'A001,A002,A003',
        diag_whole_code: '[D001, D002]',
        diag_code_prefix: 'E10;E11;E12',
        level1: '一级错误',
        age_min: 18
      }

      const result = convertExcelRowArrayFields(mockRow)

      // 验证必填字段存在
      expect(result.rule_name).toBeTruthy()
      expect(result.yb_code).toBeTruthy()
      expect(Array.isArray(result.yb_code)).toBe(true)
      
      // 验证数组字段正确转换
      expect(result.yb_code).toEqual(['A001', 'A002', 'A003'])
      expect(result.diag_whole_code).toEqual(['D001', 'D002'])
      expect(result.diag_code_prefix).toEqual(['E10', 'E11', 'E12'])
      
      // 验证非数组字段保持不变
      expect(result.level1).toBe('一级错误')
      expect(result.age_min).toBe(18)
    })
  })

  describe('错误场景测试', () => {
    it('应该处理缺少必填字段的情况', () => {
      const incompleteData = {
        // 缺少 rule_name
        yb_code: 'A001,A002',
        level1: '一级错误'
      }

      const result = convertExcelRowArrayFields(incompleteData)
      
      // 转换应该成功，但数据仍然缺少必填字段
      expect(result.yb_code).toEqual(['A001', 'A002'])
      expect(result.level1).toBe('一级错误')
      expect(result.rule_name).toBeUndefined()
    })

    it('应该处理数组字段格式异常的情况', () => {
      const malformedData = {
        rule_name: '测试规则',
        yb_code: 'A001,,A002,',  // 包含空值的数组
        diag_whole_code: '   D001  ,  D002  ',  // 包含空格的数组
        level1: '一级错误'
      }

      const result = convertExcelRowArrayFields(malformedData)
      
      // 验证数组字段正确清理
      expect(result.yb_code).toEqual(['A001', 'A002'])  // 空值应该被过滤
      expect(result.diag_whole_code).toEqual(['D001', 'D002'])  // 空格应该被清理
    })
  })
})
