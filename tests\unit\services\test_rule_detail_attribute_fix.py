"""
测试RuleDetail属性访问修复
验证所有代码都使用正确的属性名（id而不是rule_detail_id）
"""

import pytest
from unittest.mock import Mock, patch
from models.database import RuleDetail, RuleDetailStatusEnum


class TestRuleDetailAttributeFix:
    """测试RuleDetail属性访问修复"""

    @pytest.fixture
    def mock_rule_detail(self):
        """创建模拟的RuleDetail对象"""
        detail = Mock(spec=RuleDetail)
        detail.id = 123
        detail.rule_id = "test_rule_001"
        detail.rule_key = "test_rule_key"
        detail.rule_name = "测试规则"
        detail.level1 = "政策类"
        detail.status = RuleDetailStatusEnum.ACTIVE
        detail.to_dict.return_value = {
            "id": 123,
            "rule_id": "test_rule_001",
            "rule_key": "test_rule_key",
            "rule_name": "测试规则",
            "level1": "政策类",
            "yb_code": ["A001", "A002"],
            "status": "ACTIVE"
        }
        return detail

    def test_convert_rule_detail_to_cache_format_uses_correct_id(self, mock_rule_detail):
        """测试_convert_rule_detail_to_cache_format使用正确的id属性"""
        from services.rule_loader import _convert_rule_detail_to_cache_format
        
        with patch('services.rule_loader.UnifiedDataMappingEngine') as mock_engine_class:
            mock_engine = Mock()
            mock_engine.normalize_field_names.return_value = {"rule_id": "test_rule_001", "rule_name": "测试规则"}
            mock_engine.validate_data.return_value = {"is_valid": True}
            mock_engine.convert_to_structured_format.return_value = {"rule_id": "test_rule_001", "rule_name": "测试规则"}
            mock_engine_class.return_value = mock_engine

            result = _convert_rule_detail_to_cache_format(mock_rule_detail)

            # 验证使用了正确的id属性
            assert result is not None
            # 验证没有访问不存在的rule_detail_id属性
            assert not hasattr(mock_rule_detail, 'rule_detail_id')

    def test_batch_convert_uses_correct_id(self, mock_rule_detail):
        """测试批量转换使用正确的id属性"""
        from services.rule_loader import _batch_convert_rule_details_to_cache_format
        
        rule_details = [mock_rule_detail]
        
        with patch('services.rule_loader.UnifiedDataMappingEngine') as mock_engine_class:
            mock_engine = Mock()
            mock_engine.batch_normalize_field_names.return_value = [{"rule_id": "test_rule_001"}]
            mock_engine.batch_validate_data.return_value = [{"is_valid": True}]
            mock_engine.convert_to_structured_format.return_value = {"rule_id": "test_rule_001"}
            mock_engine_class.return_value = mock_engine

            result = _batch_convert_rule_details_to_cache_format(rule_details)

            # 验证批量转换成功
            assert len(result) == 1
            # 验证没有访问不存在的rule_detail_id属性
            assert not hasattr(mock_rule_detail, 'rule_detail_id')

    def test_rule_query_service_uses_correct_field(self):
        """测试RuleQueryService使用正确的字段名"""
        from services.rule_query_service import RuleQueryService
        
        mock_session = Mock()
        mock_query = Mock()
        mock_filter = Mock()
        
        # 模拟查询结果
        mock_result = Mock()
        mock_result.rule_id = "test_rule_001"
        
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_filter
        mock_filter.all.return_value = [mock_result]

        result = RuleQueryService.get_rule_detail_ids_by_rule_key(mock_session, "test_rule_key")

        # 验证查询使用了正确的字段
        mock_session.query.assert_called_once()
        # 验证返回结果正确
        assert result == ["test_rule_001"]

    def test_rule_detail_model_has_correct_attributes(self):
        """测试RuleDetail模型有正确的属性"""
        # 创建真实的RuleDetail实例
        detail = RuleDetail(
            rule_id="test_rule_001",
            rule_key="test_rule_key",
            rule_name="测试规则",
            level1="政策类",
            status=RuleDetailStatusEnum.ACTIVE
        )

        # 验证有正确的属性
        assert hasattr(detail, 'id')  # 主键ID
        assert hasattr(detail, 'rule_id')  # 规则ID
        assert hasattr(detail, 'rule_key')  # 规则键
        
        # 验证没有错误的属性名
        assert not hasattr(detail, 'rule_detail_id')

    def test_rule_detail_to_dict_includes_id(self):
        """测试RuleDetail.to_dict包含正确的id字段"""
        detail = RuleDetail(
            rule_id="test_rule_001",
            rule_key="test_rule_key",
            rule_name="测试规则",
            level1="政策类",
            status=RuleDetailStatusEnum.ACTIVE
        )

        result = detail.to_dict()

        # 验证包含正确的字段
        assert "id" in result
        assert "rule_id" in result
        assert "rule_key" in result
        
        # 验证不包含错误的字段名
        assert "rule_detail_id" not in result

    def test_error_handling_uses_correct_id(self, mock_rule_detail):
        """测试错误处理使用正确的id属性"""
        from services.rule_loader import _convert_rule_detail_to_cache_format
        
        # 模拟转换失败
        with patch('services.rule_loader.UnifiedDataMappingEngine') as mock_engine_class:
            mock_engine = Mock()
            mock_engine.normalize_field_names.side_effect = Exception("测试异常")
            mock_engine_class.return_value = mock_engine

            with pytest.raises(Exception) as exc_info:
                _convert_rule_detail_to_cache_format(mock_rule_detail)

            # 验证异常信息中使用了正确的id
            error_message = str(exc_info.value)
            # 错误信息应该包含正确的id值
            assert "123" in error_message or "test_rule_001" in error_message

    def test_logging_uses_correct_id(self, mock_rule_detail, caplog):
        """测试日志记录使用正确的id属性"""
        from services.rule_loader import _convert_rule_detail_to_cache_format
        
        with patch('services.rule_loader.UnifiedDataMappingEngine') as mock_engine_class:
            mock_engine = Mock()
            mock_engine.normalize_field_names.return_value = {"rule_id": "test_rule_001"}
            mock_engine.validate_data.return_value = {"is_valid": True}
            mock_engine.convert_to_structured_format.return_value = {"rule_id": "test_rule_001"}
            mock_engine_class.return_value = mock_engine

            _convert_rule_detail_to_cache_format(mock_rule_detail)

            # 验证日志中使用了正确的id
            log_messages = [record.message for record in caplog.records]
            # 应该有包含正确id的日志消息
            assert any("123" in msg for msg in log_messages)

    def test_structured_data_rule_id_assignment(self, mock_rule_detail):
        """测试结构化数据中rule_id字段的正确赋值"""
        from services.rule_loader import _convert_rule_detail_to_cache_format
        
        with patch('services.rule_loader.UnifiedDataMappingEngine') as mock_engine_class:
            mock_engine = Mock()
            mock_engine.normalize_field_names.return_value = {"rule_name": "测试规则"}
            mock_engine.validate_data.return_value = {"is_valid": True}
            # 模拟structured_data中没有rule_id字段
            mock_engine.convert_to_structured_format.return_value = {"rule_name": "测试规则"}
            mock_engine_class.return_value = mock_engine

            with patch('services.rule_loader._convert_array_fields_for_rules') as mock_convert:
                mock_convert.return_value = {"rule_name": "测试规则", "rule_id": "123"}
                
                result = _convert_rule_detail_to_cache_format(mock_rule_detail)

                # 验证rule_id被正确设置为字符串格式的id
                assert result["rule_id"] == "123"

    def test_batch_conversion_error_handling(self, mock_rule_detail):
        """测试批量转换的错误处理使用正确的id"""
        from services.rule_loader import _batch_convert_rule_details_to_cache_format
        
        rule_details = [mock_rule_detail]
        
        with patch('services.rule_loader.UnifiedDataMappingEngine') as mock_engine_class:
            # 模拟批量转换失败，触发降级处理
            mock_engine_class.side_effect = Exception("批量转换失败")

            with patch('services.rule_loader._convert_rule_detail_to_cache_format') as mock_convert:
                mock_convert.return_value = {"rule_id": "test_rule_001"}
                
                result = _batch_convert_rule_details_to_cache_format(rule_details)

                # 验证降级处理成功
                assert len(result) == 1
                assert result[0]["rule_id"] == "test_rule_001"
