/**
 * 数组字段转换工具测试
 */

import { describe, it, expect } from 'vitest'
import {
  isArrayField,
  convertStringToArray,
  convertExcelRowArrayFields,
  validateArrayField,
  ARRAY_FIELDS
} from '../arrayFieldConverter'

describe('arrayFieldConverter', () => {
  describe('isArrayField', () => {
    it('应该正确识别数组字段', () => {
      expect(isArrayField('yb_code')).toBe(true)
      expect(isArrayField('diag_whole_code')).toBe(true)
      expect(isArrayField('rule_name')).toBe(false)
      expect(isArrayField('level1')).toBe(false)
    })
  })

  describe('convertStringToArray', () => {
    it('应该正确转换逗号分隔的字符串', () => {
      expect(convertStringToArray('A001,A002,A003')).toEqual(['A001', 'A002', 'A003'])
      expect(convertStringToArray('A001, A002, A003')).toEqual(['A001', 'A002', 'A003'])
    })

    it('应该正确处理带方括号的字符串', () => {
      expect(convertStringToArray('[A001,A002,A003]')).toEqual(['A001', 'A002', 'A003'])
      expect(convertStringToArray('["A001","A002","A003"]')).toEqual(['A001', 'A002', 'A003'])
    })

    it('应该正确处理分号分隔的字符串', () => {
      expect(convertStringToArray('A001;A002;A003')).toEqual(['A001', 'A002', 'A003'])
      expect(convertStringToArray('A001；A002；A003')).toEqual(['A001', 'A002', 'A003'])
    })

    it('应该正确处理空值', () => {
      expect(convertStringToArray('')).toEqual([])
      expect(convertStringToArray(null)).toEqual([])
      expect(convertStringToArray(undefined)).toEqual([])
    })

    it('应该正确处理已经是数组的值', () => {
      expect(convertStringToArray(['A001', 'A002'])).toEqual(['A001', 'A002'])
    })
  })

  describe('convertExcelRowArrayFields', () => {
    it('应该正确转换行数据中的数组字段', () => {
      const rowData = {
        rule_name: '测试规则',
        yb_code: 'A001,A002,A003',
        diag_whole_code: 'D001;D002',
        level1: '一级错误'
      }

      const result = convertExcelRowArrayFields(rowData)

      expect(result.rule_name).toBe('测试规则')
      expect(result.yb_code).toEqual(['A001', 'A002', 'A003'])
      expect(result.diag_whole_code).toEqual(['D001', 'D002'])
      expect(result.level1).toBe('一级错误')
    })

    it('应该保持非数组字段不变', () => {
      const rowData = {
        rule_name: '测试规则',
        level1: '一级错误',
        age_min: 18
      }

      const result = convertExcelRowArrayFields(rowData)

      expect(result).toEqual(rowData)
    })
  })

  describe('validateArrayField', () => {
    it('应该验证数组字段格式', () => {
      const result1 = validateArrayField('yb_code', 'A001,A002,A003')
      expect(result1.valid).toBe(true)
      expect(result1.converted).toEqual(['A001', 'A002', 'A003'])

      const result2 = validateArrayField('rule_name', '测试规则')
      expect(result2.valid).toBe(true)
    })

    it('应该处理空数组字段', () => {
      const result = validateArrayField('yb_code', '')
      expect(result.valid).toBe(true)
      expect(result.converted).toEqual([])
    })
  })

  describe('ARRAY_FIELDS常量', () => {
    it('应该包含所有预期的数组字段', () => {
      const expectedFields = [
        'yb_code',
        'diag_whole_code',
        'diag_code_prefix',
        'fee_whole_code',
        'fee_code_prefix'
      ]

      expectedFields.forEach(field => {
        expect(ARRAY_FIELDS).toContain(field)
      })
    })
  })

  describe('实际场景测试', () => {
    it('应该正确处理Excel导入的药品编码数据', () => {
      // 模拟Excel中的数据格式
      const excelData = {
        rule_name: '成人用药限制规则',
        yb_code: '"A001","A002","A003"',  // Excel可能包含引号
        diag_whole_code: '[D001, D002, D003]',  // Excel可能包含方括号
        level1: '一级错误'
      }

      const converted = convertExcelRowArrayFields(excelData)

      expect(converted.yb_code).toEqual(['A001', 'A002', 'A003'])
      expect(converted.diag_whole_code).toEqual(['D001', 'D002', 'D003'])
      expect(converted.rule_name).toBe('成人用药限制规则')
      expect(converted.level1).toBe('一级错误')
    })

    it('应该正确验证转换后的数据类型', () => {
      const validation = validateArrayField('yb_code', 'A001,A002,A003')
      
      expect(validation.valid).toBe(true)
      expect(Array.isArray(validation.converted)).toBe(true)
      expect(validation.converted).toHaveLength(3)
    })
  })
})
