#!/usr/bin/env python3
"""
检查rule_detail表中的重复数据脚本

用途：
1. 检查是否存在重复的rule_id记录
2. 分析重复数据的分布情况
3. 为数据清理提供依据

作者：系统管理员
创建时间：2025-07-30
"""

import sys
import os
from typing import Dict, List, Tuple
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import settings
from models.database import RuleDetail


def check_duplicate_data():
    """检查重复数据"""
    print("=== 规则明细重复数据检查 ===")
    
    # 获取数据库连接
    database_url = settings.get_database_url()
    if not database_url:
        print("❌ 无法获取数据库连接URL")
        return False
    
    engine = create_engine(database_url)
    Session = sessionmaker(bind=engine)
    
    try:
        with Session() as session:
            print(f"✅ 数据库连接成功: {database_url.split('@')[1] if '@' in database_url else 'local'}")
            
            # 1. 检查总记录数
            total_count = session.query(RuleDetail).count()
            print(f"📊 总记录数: {total_count}")
            
            if total_count == 0:
                print("ℹ️  数据库中没有规则明细记录")
                return True
            
            # 2. 检查重复的rule_id（不考虑rule_key）
            duplicate_query = text("""
                SELECT rule_id, COUNT(*) as count
                FROM rule_detail 
                GROUP BY rule_id 
                HAVING COUNT(*) > 1
                ORDER BY count DESC, rule_id
            """)
            
            duplicates = session.execute(duplicate_query).fetchall()
            
            if not duplicates:
                print("✅ 没有发现重复的rule_id记录")
                return True
            
            print(f"⚠️  发现 {len(duplicates)} 个重复的rule_id:")
            
            total_duplicate_records = 0
            for rule_id, count in duplicates[:10]:  # 只显示前10个
                print(f"   - rule_id: {rule_id}, 重复次数: {count}")
                total_duplicate_records += count
            
            if len(duplicates) > 10:
                remaining = len(duplicates) - 10
                remaining_records = sum(count for _, count in duplicates[10:])
                print(f"   ... 还有 {remaining} 个重复的rule_id，共 {remaining_records} 条记录")
                total_duplicate_records += remaining_records
            
            print(f"📈 重复记录总数: {total_duplicate_records}")
            print(f"📈 需要清理的记录数: {total_duplicate_records - len(duplicates)}")
            
            # 3. 检查按rule_key分组的重复情况
            print("\n=== 按rule_key分组的重复情况 ===")
            duplicate_by_key_query = text("""
                SELECT rule_key, rule_id, COUNT(*) as count
                FROM rule_detail 
                GROUP BY rule_key, rule_id 
                HAVING COUNT(*) > 1
                ORDER BY rule_key, count DESC, rule_id
            """)
            
            duplicates_by_key = session.execute(duplicate_by_key_query).fetchall()
            
            if duplicates_by_key:
                print(f"⚠️  发现 {len(duplicates_by_key)} 个按(rule_key, rule_id)分组的重复记录:")
                for rule_key, rule_id, count in duplicates_by_key[:5]:
                    print(f"   - rule_key: {rule_key}, rule_id: {rule_id}, 重复次数: {count}")
                
                if len(duplicates_by_key) > 5:
                    print(f"   ... 还有 {len(duplicates_by_key) - 5} 个重复组合")
            else:
                print("✅ 按(rule_key, rule_id)分组没有发现重复记录")
            
            # 4. 分析重复数据的时间分布
            print("\n=== 重复数据时间分布分析 ===")
            time_analysis_query = text("""
                SELECT 
                    DATE(created_at) as create_date,
                    COUNT(*) as total_records,
                    COUNT(DISTINCT rule_id) as unique_rule_ids,
                    COUNT(*) - COUNT(DISTINCT rule_id) as duplicate_records
                FROM rule_detail 
                GROUP BY DATE(created_at)
                HAVING duplicate_records > 0
                ORDER BY create_date DESC
                LIMIT 10
            """)
            
            time_analysis = session.execute(time_analysis_query).fetchall()
            
            if time_analysis:
                print("📅 按日期统计的重复情况:")
                for date, total, unique, duplicates in time_analysis:
                    print(f"   - {date}: 总记录 {total}, 唯一rule_id {unique}, 重复记录 {duplicates}")
            else:
                print("ℹ️  没有按日期统计到重复记录")
            
            return len(duplicates) == 0
            
    except Exception as e:
        print(f"❌ 检查过程中发生错误: {e}")
        return False
    finally:
        engine.dispose()


def main():
    """主函数"""
    print("开始检查rule_detail表中的重复数据...")
    
    success = check_duplicate_data()
    
    if success:
        print("\n✅ 重复数据检查完成")
        sys.exit(0)
    else:
        print("\n❌ 重复数据检查失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
